import {
  router,
  mergeRouters,
  tableResultProcedure,
  singleResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma, Prisma } from "@/lib/prisma";
import {
  materielCreateSchema,
  materielQuerySchema,
  materielUpdateSchema,
} from "~/schemas/materiel";
export const queryMateriel = tableResultProcedure
  .meta({ permission: ["materiel:query"], authRequired: true })
  .input(materielQuerySchema)
  .query(async ({ input }) => {
    const { id, code, name, model, quick } = input;
    const where: Prisma.MaterielWhereInput = {};
    if (quick) {
      where.OR = [
        { code: { contains: quick } },
        { name: { contains: quick } },
        { model: { contains: quick } },
      ];
    }
    if (id) where.id = id;
    if (code) where.code = code;
    if (name) {
      where.name = {
        contains: name,
      };
    }
    if (model) where.model = model;
    const material = await prisma.materiel.findManyWithCount({
      take: input.take,
      skip: input.skip,
      where: where,
    });
    return { code: 1, message: "success", data: material };
  });

export const addMateriel = singleResultProcedure
  .meta({ permission: ["materiel:add"], authRequired: true })
  .input(materielCreateSchema)
  .mutation(async ({ input, ctx }) => {
    const [category, attribute, type] = input.type;
    const prefix = `${category}${attribute}${type}`;

    const materiel = await prisma.$transaction(async (tx) => {
      const lastMateriel = await tx.materiel.findFirst({
        where: {
          category,
          attribute,
          type,
        },
        orderBy: {
          code: "desc",
        },
      });

      let sequence = "0001";
      if (lastMateriel) {
        const lastSequence = lastMateriel.code.slice(-4);
        sequence = String(Number(lastSequence) + 1).padStart(4, "0");
      }

      let code = `${prefix}${sequence}`;
      let isUnique = false;

      while (!isUnique) {
        const existingMateriel = await tx.materiel.findUnique({
          where: { code },
        });

        if (!existingMateriel) {
          isUnique = true;
        } else {
          const currentSequence = Number(sequence);
          sequence = String(currentSequence + 1).padStart(4, "0");
          code = `${prefix}${sequence}`;
        }
      }

      return await tx.materiel.create({
        data: {
          category: input.type[0],
          attribute: input.type[1],
          type: input.type[2],
          name: input.name,
          code,
          unit: input.unit,
          specification: input.specification,
          model: input.model,
          description: input.description,
          Admin: {
            connect: {
              id: ctx.user?.id,
            },
          },
        },
      });
    });

    return { code: 1, message: "success", data: materiel };
  });

export const updateMateriel = singleResultProcedure
  .meta({ permission: ["materiel:update"], authRequired: true })
  .input(materielUpdateSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const oldMateriel = await prisma.materiel.findUnique({
        where: { id: input.id },
      });

      let code = input.code;
      if (
        !oldMateriel ||
        oldMateriel.category !== input.category ||
        oldMateriel.attribute !== input.attribute ||
        oldMateriel.type !== input.type
      ) {
        const prefix = `${input.category}${input.attribute}${input.type}`;

        const result = await prisma.$transaction(async (tx) => {
          const lastMateriel = await tx.materiel.findFirst({
            where: {
              category: input.category,
              attribute: input.attribute,
              type: input.type,
            },
            orderBy: {
              code: "desc",
            },
          });

          let sequence = "0001";
          if (lastMateriel) {
            const lastSequence = lastMateriel.code.slice(-4);
            sequence = String(Number(lastSequence) + 1).padStart(4, "0");
          }

          let newCode = `${prefix}${sequence}`;
          let isUnique = false;

          while (!isUnique) {
            const existingMateriel = await tx.materiel.findUnique({
              where: { code: newCode },
            });

            if (!existingMateriel) {
              isUnique = true;
            } else {
              const currentSequence = Number(sequence);
              sequence = String(currentSequence + 1).padStart(4, "0");
              newCode = `${prefix}${sequence}`;
            }
          }

          return newCode;
        });

        code = result;
      }

      const materiel = await prisma.materiel.update({
        where: {
          id: input.id,
        },
        data: {
          category: input.category,
          attribute: input.attribute,
          type: input.type,
          name: input.name,
          code,
          unit: input.unit,
          specification: input.specification,
          model: input.model,
          useable: input.useable,
          description: input.description,
        },
      });

      return { code: 1, message: "success", data: materiel };
    } catch (error: any) {
      console.error("Update materiel error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: error.message || "更新物料失败",
      });
    }
  });

export default router({
  queryMateriel,
  addMateriel,
  updateMateriel,
});
